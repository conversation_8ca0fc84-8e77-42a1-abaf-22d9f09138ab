from typing import Any, List, Optional
from pydantic import Field
from llama_index.core.embeddings import BaseEmbedding
from sentence_transformers import SentenceTransformer
import torch


class SentenceTransformersEmbedding(BaseEmbedding):
    """
    Custom embedding class that integrates SentenceTransformers with LlamaIndex's BaseEmbedding.
    https://docs.llamaindex.ai/en/stable/module_guides/models/embeddings/
    """


    device: str = Field(
        default_factory=lambda: "cuda" if torch.cuda.is_available() else "cpu"
    )
    use_multi_gpu: bool = Field(
        default=False, description="Enable or disable multi-GPU support."
    )
    target_devices: List[str] = Field(
        default_factory=lambda: ["cuda:0"],
        description="List of GPU device IDs to use (e.g., ['cuda:0', 'cuda:1']).",
    )
    batch_size: int = Field(
        default=32, description="Batch size for multi-GPU processing."
    )

    def __init__(
        self,
        model_name: str = "all-MiniLM-L6-v2",
        cache_folder: Optional[str] = None,
        **kwargs: Any,
    ) -> None:
        """
        Args:
            model_name (str): The name of the SentenceTransformer model.
            use_multi_gpu (bool): Whether to use multiple GPUs for inference.
            cache_folder (str): Directory for caching the model.
            target_devices (List[str]): List of GPU device identifiers (e.g., ['cuda:0', 'cuda:1']).
            batch_size (int): Batch size for multi-GPU processing.
        """
        super().__init__(**kwargs)

        # SentenceTransformer model initialization
        self._model = SentenceTransformer(model_name, cache_folder=cache_folder)
        self._model.to(self.device)

    def _get_query_embedding(self, query: str) -> List[float]:
        """
        Synchronous embedding of a single query.
        """
        return self._get_text_embedding(query)

    async def _aget_query_embedding(self, query: str) -> List[float]:
        """
        Asynchronous embedding of a single query.
        """
        return self._get_query_embedding(query)

    def _get_text_embedding(self, text: str) -> List[float]:
        """
        Synchronous embedding of a single text.
        """
        embeddings = self._model.encode(
            [text],
            convert_to_numpy=True,
            device=self.device,
            show_progress_bar=False,
        )
        return embeddings[0].tolist()

    async def _aget_text_embedding(self, text: str) -> List[float]:
        """
        Asynchronous embedding of a single text.
        """
        return self._get_text_embedding(text)

    def _get_text_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Synchronous embedding of a list of texts.
        """
        if self.use_multi_gpu and len(self.target_devices) > 1:
            return self._process_multi_gpu(texts)
        embeddings = self._model.encode(
            texts,
            convert_to_numpy=True,
            device=self.device,
            show_progress_bar=False,
        )
        return embeddings.tolist()

    async def _aget_text_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Asynchronous embedding of a list of texts.
        """
        return self._get_text_embeddings(texts)

    def _process_multi_gpu(self, texts: List[str]) -> List[List[float]]:
        """
        Multi-GPU embedding using SentenceTransformers' multi-process pool.
        """
        pool = self._model.start_multi_process_pool(target_devices=self.target_devices)
        embeddings = self._model.encode_multi_process(
            texts, pool=pool, batch_size=self.batch_size
        )
        self._model.stop_multi_process_pool(pool)
        return embeddings.tolist()
