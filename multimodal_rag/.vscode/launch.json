{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "multimodal_rag",
            "type": "debugpy",
            "request": "launch",
            "module": "streamlit",
            "python": "${command:python.interpreterPath}",
            "args": [
                "run",
                "app.py",
            ],
            "autoStartBrowser": true,
        },
        {
            "name": "extract_text_and_view",
            "type": "debugpy",
            "request": "launch",
            "module": "streamlit",
            "python": "${command:python.interpreterPath}",
            "args": [
                "run",
                "extract_and_view_documents/appviewadv.py",
            ],
            "autoStartBrowser": true,
        }
    ]
}
