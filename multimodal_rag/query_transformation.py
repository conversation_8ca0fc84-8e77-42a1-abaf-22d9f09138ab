from openai import AsyncOpenAI
from dotenv import load_dotenv
import logging
import time
import json
import os
import base64

from qdrant_client import QdrantClient

qdrant_client = QdrantClient("http://localhost:6333", timeout=60.0)

load_dotenv()

# Configurazione del logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# docker run -itd --rm --name Mistral-Small-24B-Instruct-2501-FP8-Dynamic \
#     --runtime nvidia \
#     --gpus '"device=0"' \
#     -v /data/datascience/.shared/.transformers_cache:/data/datascience/.shared/.transformers_cache \
#     -p 8081:8081 \
#     vllm/vllm-openai:v0.7.2.updated-transformers \
#     --model "neuralmagic/Mistral-Small-24B-Instruct-2501-FP8-Dynamic" \
#     --port 8081 \
#     --gpu_memory_utilization 0.9 \
#     --enable-prefix-caching \
#     --max_model_len 32768 \
#     --tensor_parallel_size 1 \
#     --api-key token-abc123 \
#     --download-dir /data/datascience/.shared/.transformers_cache


# docker run -itd --rm \
#     --gpus '"device=0"' \
#     -v /home/<USER>/.transformers_cache:/home/<USER>/.transformers_cache \
#     -p 8090:8090 \
#     --name multilingual-e5-large-instruct \
#     vllm/vllm-openai:latest \
#     --model intfloat/multilingual-e5-large-instruct \
#     --port 8090 \
#     --dtype float32 \
#     --tensor-parallel-size 1 \
#     --max-model-len 512 \
#     --task embed \
#     --download-dir home/.shared/.transformers_cache


class VLLMEngine:
    def __init__(self, client, model):
        self.client = client
        self.model = model

    async def generate_response(
        self,
        system_content,
        user_content,
        stream,
        extra_body=None,
        chat_history=[],
    ):
        messages = (
            [{"role": "system", "content": system_content}]
            + chat_history
            + [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": user_content,
                        },
                    ],
                }
            ]
        )

        # print(messages)

        response = await self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            stream=stream,
            extra_body=extra_body,
        )

        if stream:
            async for chunk in response:
                yield chunk.choices[0].delta.content or ""
        else:
            yield response.choices[0].message.content

VLLM_DISTRIBUTION = os.getenv("VLLM_DISTRIBUTION")
VLLM_ENDPOINT = os.getenv("VLLM_ENDPOINT")
VLLM_API_KEY = os.getenv("VLLM_API_KEY")
VLLM_MODEL = VLLMEngine(
    client=AsyncOpenAI(
        base_url=VLLM_ENDPOINT,
        api_key=VLLM_API_KEY,
    ),
    model=VLLM_DISTRIBUTION,
)

EMBEDDER_RAG_MODEL = os.getenv("EMBEDDER_RAG_MODEL")
MULTIMODAL_RAG_QDRANT_COLLECTION_DOCUMENTS = os.getenv("MULTIMODAL_RAG_QDRANT_COLLECTION_DOCUMENTS")
vllm_embedding_client = AsyncOpenAI(
    base_url=f"http://localhost:{os.getenv("EMBEDDER_RAG_PORT")}/v1",
    api_key="not-needed",
)


def get_b64_image_from_content(image_content):
    """
    Convert image content to Base64 string.

    Args:
        image_content (bytes): Binary content of the image.

    Returns:
        str: Base64 encoded string of the image.
    """
    return base64.b64encode(image_content).decode("utf-8")


async def describe_image(image_content, stream=False):
    """
    Asynchronously describe an image using Qwen/Qwen2-VL-7B-Instruct model served via vLLM.

    Args:
        image_content (bytes): Binary content of the image.

    Returns:
        str: The description generated by the model.
    """
    # Convert the image to base64
    image_b64 = get_b64_image_from_content(image_content)

    # Initialize the AsyncOpenAI client
    client = AsyncOpenAI(
        base_url=VLLM_ENDPOINT,  # Replace with your vLLM server URL
        api_key=VLLM_API_KEY,  # Replace with your actual API key
    )

    guided_schema = {
        "type": "string",
        "enum": ["yes", "no"],  # Limita le risposte a "yes" o "no"
    }
    extra_body = {
        "guided_decoding_backend": "xgrammar",
        "guided_json": json.dumps(guided_schema),
    }

    try:
        # Prepare the payload for the chat completion
        response = await client.chat.completions.create(
            model=VLLM_DISTRIBUTION,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Does this image contain any kind of plot or chart, such as a line plot, bar chart, scatter plot, or similar visual representation of data?",
                        },
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{image_b64}"},
                        },
                    ],
                }
            ],
            stream=stream,
            max_tokens=512,
            temperature=0.7,
            top_p=0.9,
            extra_body=extra_body,
        )
        logger.info("Does this image contain any kind of plot or chart?")

        # Extract and return the description
        if stream:
            async for chunk in response:
                yield chunk.choices[0].delta.content or ""
        else:
            yield response.choices[0].message.content

    except Exception as e:
        logger.error(f"Error describing image due to: {e}")


async def check_policy_compliance(query):
    system_content = (
        "You are a security and compliance expert for Gi Group Holding. "
        "Your responsibility is to analyze user queries and determine if they violate the company's policies, ethical guidelines, or acceptable use standards. "
        "Consider issues such as inappropriate language, data privacy violations, security risks, or attempts to access confidential or restricted information. "
        "Respond with 'yes' only if the query violates company policies; otherwise, respond with 'no'."
    )

    guided_schema = {
        "type": "string",
        "enum": ["yes", "no"],  # Limita le risposte a "yes" o "no"
    }
    extra_body = {
        "guided_decoding_backend": "xgrammar",
        "guided_json": json.dumps(guided_schema),
    }

    response = VLLM_MODEL.generate_response(
        system_content=system_content,
        user_content=query,
        stream=False,
        extra_body=extra_body,
    )
    answer = [answer.strip().lower() async for answer in response]

    return "yes" in answer[0]


async def analyze_query(query):
    prompt = f"""
        Analyze the following query and determine if it requires retrieval from
        the knowledge base of Gi Group Holding. Answer with 'yes' only if the question
        needs information specific to Gi Group Holding, such as policies, internal processes,
        or company-specific details. Answer 'no' to general or unrelated questions.
        Query: {query}
        """

    guided_schema = {
        "type": "string",
        "enum": ["yes", "no"],  # Limita le risposte a "yes" o "no"
    }
    extra_body = {
        "guided_decoding_backend": "xgrammar",
        "guided_json": json.dumps(guided_schema),
    }

    start_time = time.time()
    response = VLLM_MODEL.generate_response(
        system_content="You are a system analyzing queries.",
        user_content=prompt,
        stream=False,
        extra_body=extra_body,
    )
    answer = [answer.strip().lower() async for answer in response]
    logger.info("Is RAG necessary?: %s", answer)
    elapsed_time = time.time() - start_time
    logger.info(f"Tempo di esecuzione: {elapsed_time:.2f} s")

    return "yes" in answer[0]


async def paraphrase_query(query):
    system_content = (
        "Reformulate the following user query to make it clearer, more precise, and better suited for retrieving relevant information from a knowledge base. "
        "Ensure that **the meaning is preserved** and that you **maintain the original language** of the query. "
        "This reformulation will be used to improve the accuracy of a retrieval-augmented generation (RAG) system.\n\n"
    )
    response = VLLM_MODEL.generate_response(
        system_content=system_content,
        user_content=query,
        stream=False,
    )
    answer = [answer.strip() async for answer in response][0]
    logger.info("paraphrase_query: %s", answer)

    return answer


async def answer_question(query):
    response = VLLM_MODEL.generate_response(
        system_content="You are a helpful assistant answering general questions.",
        user_content=query,
        stream=True,
    )
    async for chunk in response:
        print(chunk or "", end="")

    print("\n")


async def retrieve_information(query):
    # Generate query embedding
    response = await vllm_embedding_client.embeddings.create(
        model=EMBEDDER_RAG_MODEL,
        input=[query],  # Puoi anche fare batch qui, se vuoi
        encoding_format="float",  # Di solito restituisce float32
    )

    embedding_vllm = response.data[0].embedding

    # Retrieve relevant documents from Qdrant
    results = qdrant_client.search(
        collection_name=MULTIMODAL_RAG_QDRANT_COLLECTION_DOCUMENTS, query_vector=embedding_vllm, limit=5
    )

    results = [result.payload for result in results]

    query += (
        "\n\nYou may use the following internal sources to answer the question, but **only if they provide relevant information**. "
        "If the sources are **not sufficient to confidently answer**, respond with one of the following options:\n"
        "- Say that you **do not have enough internal information to provide an accurate answer**.\n"
        "- OR suggest that **the user provides more details or clarifies their request**, if you think additional information could help you provide a better response.\n\n"
        "Respond **in the same language as the user's query** to ensure clarity and understanding.\n"
    )
    for res in results:
        if res["type"] == "text":
            query += "source:" + results[0]["source"] + "\n"
            query += "content:" + json.loads(res["_node_content"])["text"] + "\n\n"

    return query


async def pipeline(query):
    # 1. Check compliance policy
    if await check_policy_compliance(query):
        print(
            "We are unable to process your request as it does not comply with company policies.\n"
        )

    if await analyze_query(query):
        paraphrased_query = await paraphrase_query(query)
        query = await retrieve_information(paraphrased_query)

    await answer_question(query)


async def vision_pipeline():
    # Descrizione immagine di esempio
    with open(
        "/home/<USER>/workarea/multimodal_rag/vectorstore/image_references/image23-page4.png",
        "rb",
    ) as image_file:
        image_content = image_file.read()

        # Utilizza un ciclo asincrono per raccogliere i risultati dall'async generator
        response = ""
        async for chunk in describe_image(image_content):
            response += chunk

        return response


if __name__ == "__main__":
    import asyncio

    # query = "Chi sei tu?"
    query = "Chi è il capo di gigroup holding?"
    # query = "Chi è il più coglione di tutti?"
    # query = "Quali sono le notizie più interessanti? https://www.nbcnews.com/business"
    # query = "Mi riassumi il contenuto? https://it.wikipedia.org/wiki/Gi_Group"

    asyncio.run(pipeline(query))

    # answer = asyncio.run(vision_pipeline())
    # logger.info(answer)
