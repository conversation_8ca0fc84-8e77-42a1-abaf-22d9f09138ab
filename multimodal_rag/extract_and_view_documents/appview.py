import streamlit as st
from PIL import Image
import pandas as pd
from document_processors import load_multimodal_data


def display_extracted_content(documents):
    """Visualizza il contenuto estratto dal documento."""
    for doc in documents:
        doc_type = doc.metadata.get("type", "unknown")
        if doc_type == "text":
            st.markdown(f"**<PERSON><PERSON> (Pagina {doc.metadata['page_num']}):**")
            st.text_area("Testo", doc.text, height=200)
        elif doc_type == "image":
            st.markdown(f"**Immagine Estratta (Pagina {doc.metadata['page_num']}):**")
            st.image(Image.open(doc.metadata["image"]), caption=doc.metadata["caption"])
        elif doc_type == "table":
            st.markdown(f"**Tabella Estratta (Pagina {doc.metadata['page_num']}):**")
            df = pd.read_excel(doc.metadata["dataframe"])
            st.dataframe(df)
            st.markdown(f"*Descrizione:* {doc.metadata['caption']}")
        else:
            st.warning(f"Tipo sconosciuto: {doc_type}")


def main():
    st.title("Document Processing Viewer")
    uploaded_files = st.file_uploader("Carica i tuoi file", accept_multiple_files=True)

    if uploaded_files:
        # Processa i file caricati
        st.info("Processando i file...")
        documents = load_multimodal_data(uploaded_files)
        st.success(f"{len(documents)} elementi estratti dai documenti caricati.")

        # Visualizza il contenuto
        display_extracted_content(documents)


if __name__ == "__main__":
    main()
