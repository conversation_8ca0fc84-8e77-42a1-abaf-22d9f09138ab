import streamlit as st
import fitz  # PyMuPDF
import os
from PIL import Image
import plotly.graph_objects as go
from streamlit_plotly_events import plotly_events

# Configure the app to use full width
st.set_page_config(layout="wide")


def extract_pdf_content_with_metadata(pdf_path, output_folder="extracted_pages"):
    """
    Extract content, images and coordinates from PDF and save pages to specific folder.
    """
    os.makedirs(output_folder, exist_ok=True)
    doc = fitz.open(pdf_path)
    pages = []

    for page_num in range(len(doc)):
        page = doc.load_page(page_num)
        pix = page.get_pixmap()

        image_path = os.path.join(output_folder, f"page_{page_num + 1}.png")
        pix.save(image_path)

        text_blocks = page.get_text("blocks")
        extracted_texts = [
            {
                "text": block[4],
                "bbox": block[:4],
                "type": "text",
                "page": page_num + 1,
            }
            for block in text_blocks
        ]

        pages.append({"image": image_path, "content": extracted_texts})

    doc.close()
    return pages


def draw_plotly_page(image_path, content):
    """
    Create a Plotly figure with background image and bounding boxes.
    """
    img = Image.open(image_path)
    width, height = img.size

    fig = go.Figure()

    # Add image as background with fixed positioning
    fig.add_layout_image(
        dict(
            source=img,
            xref="paper",
            yref="paper",
            x=0,
            y=1,
            sizex=1,
            sizey=1,
            sizing="stretch",
            opacity=1,
            layer="below",
        )
    )

    # Draw bounding boxes
    for item in content:
        x0, y0, x1, y1 = item["bbox"]
        color = (
            "rgba(255, 0, 0, 0.3)" if item["type"] == "text" else "rgba(0, 0, 255, 0.3)"
        )
        fig.add_shape(
            type="rect",
            x0=x0,
            y0=y0,
            x1=x1,
            y1=y1,
            line=dict(color=color.replace("0.3", "1"), width=1),
            fillcolor=color,
            opacity=0.3,
        )

    # Configure layout with better stabilization
    fig.update_layout(
        autosize=False,
        width=900,
        height=1200,
        margin=dict(l=0, r=0, t=0, b=0),
        plot_bgcolor="rgba(0,0,0,0)",
        paper_bgcolor="rgba(0,0,0,0)",
        showlegend=False,
        dragmode="select",
        clickmode="event",
        hovermode=False,
    )

    # Fix axes to prevent unwanted scaling
    fig.update_xaxes(visible=False, range=[0, width], fixedrange=True)
    fig.update_yaxes(visible=False, range=[height, 0], fixedrange=True)

    return fig


def find_clicked_text_scaled(
    click_x, click_y, content, image_width, image_height, tolerance=5
):
    """
    Trova il blocco di testo che contiene le coordinate cliccate con una migliore precisione,
    adattandosi alle dimensioni dell'immagine e aggiungendo tolleranza.

    :param click_x: Coordinata X del click
    :param click_y: Coordinata Y del click
    :param content: Lista dei blocchi di testo estratti con bounding box
    :param image_width: Larghezza dell'immagine della pagina PDF
    :param image_height: Altezza dell'immagine della pagina PDF
    :param tolerance: Margine di tolleranza per migliorare il rilevamento del click
    :return: Testo del blocco corrispondente o None se nessun match è trovato
    """
    for item in content:
        if item["type"] == "text":
            x0, y0, x1, y1 = item["bbox"]

            # Normalizzazione rispetto all'altezza dell'immagine
            norm_x0 = x0 / image_width
            norm_y0 = y0 / image_height
            norm_x1 = x1 / image_width
            norm_y1 = y1 / image_height

            # Normalizzazione delle coordinate del click
            norm_click_x = click_x / image_width
            norm_click_y = click_y / image_height

            # Controllo se il click rientra nel bounding box con tolleranza
            if (norm_x0 - tolerance / image_width) <= norm_click_x <= (
                norm_x1 + tolerance / image_width
            ) and (norm_y0 - tolerance / image_height) <= norm_click_y <= (
                norm_y1 + tolerance / image_height
            ):
                return item["text"], True

    return None, False


def main():
    st.title("PDF Viewer with Interactive Bounding Boxes")

    # Initialize session state
    if "selected_text" not in st.session_state:
        st.session_state.selected_text = ""
    if "current_page" not in st.session_state:
        st.session_state.current_page = 1
    if "pages" not in st.session_state:
        st.session_state.pages = None
    if "last_click" not in st.session_state:
        st.session_state.last_click = None

    base_dir = "/home/<USER>/workarea/multimodal_rag/extract_and_view_documents/.tmp"
    docs_path = os.path.join(base_dir, "docs")
    os.makedirs(docs_path, exist_ok=True)

    uploaded_file = st.file_uploader("Upload a PDF", type=["pdf"])

    if uploaded_file:
        pdf_path = os.path.join(docs_path, "uploaded.pdf")
        with open(pdf_path, "wb") as f:
            f.write(uploaded_file.read())

        # Only process PDF if it's newly uploaded or pages haven't been extracted
        if st.session_state.pages is None:
            output_folder = os.path.join(base_dir, ".images")
            os.makedirs(output_folder, exist_ok=True)
            st.session_state.pages = extract_pdf_content_with_metadata(
                pdf_path, output_folder
            )

        # Page selection
        if len(st.session_state.pages) > 1:
            page_num = st.slider(
                "Select Page",
                1,
                len(st.session_state.pages),
                st.session_state.current_page,
            )
            if page_num != st.session_state.current_page:
                st.session_state.current_page = page_num
                st.session_state.selected_text = ""  # Reset text when changing page

        selected_page = st.session_state.pages[st.session_state.current_page - 1]

        # Create two columns with fixed ratio
        left_col, right_col = st.columns([2.5, 1])

        with left_col:
            st.subheader("PDF Page with Bounding Boxes")
            fig = draw_plotly_page(selected_page["image"], selected_page["content"])
            clicked_points = plotly_events(
                fig,
                click_event=True,
                select_event=True,
                hover_event=False,
                override_height=1200,
                key=f"plot_{st.session_state.current_page}",
            )
            st.write("Clicked points:", clicked_points)

            # Handle clicks with better state management
            if clicked_points:
                current_click = clicked_points[0]
                # Check if this is a new click
                if (
                    st.session_state.last_click is None
                    or current_click != st.session_state.last_click
                ):
                    click_x = current_click["x"]
                    click_y = current_click["y"]
                    img = Image.open(selected_page["image"])
                    image_width, image_height = img.size
                    new_text, found = find_clicked_text_scaled(
                        click_x,
                        click_y,
                        selected_page["content"],
                        image_width,
                        image_height,
                    )

                    if found:
                        st.session_state.selected_text = new_text
                        st.session_state.last_click = current_click

        with right_col:
            st.subheader("Extracted Text")
            text_area = st.empty()
            if st.session_state.selected_text:
                text_area.text_area(
                    "Selected text:",
                    value=st.session_state.selected_text,
                    height=400,
                    key=f"text_display_{st.session_state.current_page}_{hash(st.session_state.selected_text)}",
                )
            else:
                text_area.write("Click on a highlighted area to view the text")


if __name__ == "__main__":
    main()
