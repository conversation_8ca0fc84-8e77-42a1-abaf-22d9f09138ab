import streamlit as st
import plotly.graph_objects as go
from streamlit_plotly_events import plotly_events

st.set_page_config(layout="wide")

# Creazione di una figura di test
fig = go.Figure()
fig.add_trace(go.Scatter(x=[1, 2, 3], y=[1, 4, 9], mode="markers"))

fig.update_layout(
    clickmode="event+select",
    dragmode="select"
)

st.write("Click sulla figura:")
clicked_points = plotly_events(
    fig,
    click_event=True,
    select_event=True,
    hover_event=False,
)

st.write("Punti cliccati:", clicked_points)
