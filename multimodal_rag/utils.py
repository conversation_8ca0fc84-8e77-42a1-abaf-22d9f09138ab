import os
import base64
import fitz
from io import BytesIO
from PIL import Image
from dotenv import load_dotenv
from openai import OpenAI

load_dotenv()

VLLM_DISTRIBUTION = os.getenv("VLLM_DISTRIBUTION")
VLLM_ENDPOINT = os.getenv("VLLM_ENDPOINT")
VLLM_API_KEY = os.getenv("VLLM_API_KEY")

def get_b64_image_from_content(image_content):
    """Convert image content to base64 encoded string."""
    img = Image.open(BytesIO(image_content))
    if img.mode != "RGB":
        img = img.convert("RGB")
    buffered = BytesIO()
    img.save(buffered, format="JPEG")
    return base64.b64encode(buffered.getvalue()).decode("utf-8")


def process_graph(image_content):
    """Determine if an image is a graph, plot, chart, or table."""
    res = describe_image(image_content)
    is_graph = any(
        keyword in res.lower() for keyword in ["graph", "plot", "chart", "table"]
    )

    if is_graph:
        return res
    else:
        return " "

def describe_image(image_content):
    """
    Asynchronously describe an image using Qwen/Qwen2-VL-7B-Instruct model served via vLLM.
    https://docs.vllm.ai/en/stable/getting_started/examples/openai_chat_completion_client_for_multimodal.html

    Args:
        image_path (str): Path to the image file to describe.

    Returns:
        str: The description generated by the model.
    """
    # Convert the image to base64
    image_b64 = get_b64_image_from_content(image_content)

    # Initialize the AsyncOpenAI client
    client = OpenAI(
        base_url=VLLM_ENDPOINT,
        api_key=VLLM_API_KEY,
    )

    try:
        # Prepare the payload for the chat completion
        completion = client.chat.completions.create(
            model=VLLM_DISTRIBUTION,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "What's in this image?"},
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{image_b64}"},
                        },
                    ],
                }
            ],
            max_tokens=512,
            temperature=0.7,
            top_p=0.9,
        )

        # Extract and return the description
        return completion.choices[0].message.content
    
    except Exception as e:
        # Log dell'errore (opzionale, puoi usare logging invece di print)
        print(f"Error describing image due to: {e}")
        return ""


def extract_text_around_item(text_blocks, bbox, page_height, threshold_percentage=0.1):
    """Extract text above and below a given bounding box on a page."""
    before_text, after_text = "", ""
    vertical_threshold_distance = page_height * threshold_percentage
    horizontal_threshold_distance = bbox.width * threshold_percentage

    for block in text_blocks:
        block_bbox = fitz.Rect(block[:4])
        vertical_distance = min(
            abs(block_bbox.y1 - bbox.y0), abs(block_bbox.y0 - bbox.y1)
        )
        horizontal_overlap = max(
            0, min(block_bbox.x1, bbox.x1) - max(block_bbox.x0, bbox.x0)
        )

        if (
            vertical_distance <= vertical_threshold_distance
            and horizontal_overlap >= -horizontal_threshold_distance
        ):
            if block_bbox.y1 < bbox.y0 and not before_text:
                before_text = block[4]
            elif block_bbox.y0 > bbox.y1 and not after_text:
                after_text = block[4]
                break

    return before_text, after_text


def process_text_blocks(text_blocks, char_count_threshold=3000):
    """Group text blocks based on a character count threshold."""
    current_group = []
    grouped_blocks = []
    current_char_count = 0

    for block in text_blocks:
        if block[-1] == 0:  # Check if the block is of text type
            block_text = block[4]
            block_char_count = len(block_text)

            if current_char_count + block_char_count <= char_count_threshold:
                current_group.append(block)
                current_char_count += block_char_count
            else:
                if current_group:
                    grouped_content = "\n".join([b[4] for b in current_group])
                    grouped_blocks.append((current_group[0], grouped_content))
                current_group = [block]
                current_char_count = block_char_count

    # Append the last group
    if current_group:
        grouped_content = "\n".join([b[4] for b in current_group])
        grouped_blocks.append((current_group[0], grouped_content))

    return grouped_blocks


def save_uploaded_file(uploaded_file):
    """Save an uploaded file to a temporary directory."""
    temp_dir = os.path.join(os.getcwd(), "vectorstore", "ppt_references", "tmp")
    os.makedirs(temp_dir, exist_ok=True)
    temp_file_path = os.path.join(temp_dir, uploaded_file.name)

    with open(temp_file_path, "wb") as temp_file:
        temp_file.write(uploaded_file.read())

    return temp_file_path
