import os
import streamlit as st
import qdrant_client
import logging
from dotenv import load_dotenv
from llama_index.core import Settings
from llama_index.core import VectorStoreIndex, StorageContext
from llama_index.core.node_parser import SentenceSplitter

from huggingface_embd import SentenceTransformersEmbedding

# from llama_index.vector_stores.milvus import MilvusVectorStore
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.llms.openai_like import OpenAILike

from document_processors import load_multimodal_data, load_data_from_directory

# Set up logging
logging.basicConfig(level=logging.INFO)


# Set up the page configuration
st.set_page_config(layout="wide")

load_dotenv()

# Get environment variables
VLLM_DISTRIBUTION = os.getenv("VLLM_DISTRIBUTION")
VLLM_ENDPOINT = os.getenv("VLLM_ENDPOINT")
VLLM_API_KEY = os.getenv("VLLM_API_KEY")
EMBEDDER_RAG_MODEL = os.getenv("EMBEDDER_RAG_MODEL")
TRANSFORMERS_CACHE = os.getenv("TRANSFORMERS_CACHE")
MULTIMODAL_RAG_QDRANT_PORT = os.getenv("MULTIMODAL_RAG_QDRANT_PORT")
MULTIMODAL_RAG_QDRANT_COLLECTION_DOCUMENTS = os.getenv("MULTIMODAL_RAG_QDRANT_COLLECTION_DOCUMENTS")

# Initialize settings
def initialize_settings(
    chunk_size=800,
    chunk_overlap=200,  # Introduci un overlap per fornire contesto aggiuntivo)
):
    """
    Initialize embedding models and LLMs, and configure the text splitter
    to allow for larger chunks.
    """

    # Larger language model embeddings with SentenceTransformers
    Settings.embed_model = SentenceTransformersEmbedding(
        model_name=EMBEDDER_RAG_MODEL,
        cache_folder=TRANSFORMERS_CACHE,
        target_devices=["cuda:0", "cuda:1", "cuda:2", "cuda:3"],
        batch_size=64,
    )

    # IMPORTANTE: If using the OpenAI-API vLLM server, please see the `OpenAILike` LLM class.
    # https://github.com/run-llama/llama_index/blob/70ff5e0215b71b5566440b128a554deac33fdee5/llama-index-integrations/llms/llama-index-llms-vllm/llama_index/llms/vllm/base.py#L325
    # https://github.com/run-llama/llama_index/blob/70ff5e0215b71b5566440b128a554deac33fdee5/llama-index-integrations/llms/llama-index-llms-openai-like/llama_index/llms/openai_like/base.py#L24
    Settings.llm = OpenAILike(
        model=VLLM_DISTRIBUTION,
        api_base=VLLM_ENDPOINT,
        api_key=VLLM_API_KEY,
        max_tokens=512,  # Aumenta il limite massimo di token generati
        temperature=0.7,  # Parametro per controllare la creatività
    )

    Settings.text_splitter = SentenceSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
    )


# Create index from documents
def create_or_load_index(documents=None):
    # Configura il client Qdrant
    client = qdrant_client.QdrantClient(
        host="localhost",
        port=MULTIMODAL_RAG_QDRANT_PORT,
    )

    collection_name = MULTIMODAL_RAG_QDRANT_COLLECTION_DOCUMENTS

    # Verifica se la collection esiste
    if collection_name in [col.name for col in client.get_collections().collections]:
        st.info("Collection found. Loading existing index...")
        vector_store = QdrantVectorStore(
            client=client,
            collection_name=collection_name,
        )
        storage_context = StorageContext.from_defaults(vector_store=vector_store)
        index = VectorStoreIndex.from_vector_store(
            vector_store,
            storage_context=storage_context,
        )
    else:
        if documents is None:
            st.error("No documents provided for creating a new index!")
            return None
        st.info("Collection not found. Creating new index...")
        vector_store = QdrantVectorStore(
            client=client,
            collection_name=collection_name,
        )
        storage_context = StorageContext.from_defaults(vector_store=vector_store)
        index = VectorStoreIndex.from_documents(
            documents,
            storage_context=storage_context,
        )

    # query_engine = index.as_query_engine(similarity_top_k=10, streaming=True)
    # response = query_engine.query("What is Extreme Multi-Label Skill Extraction Training using Large Language Models?")
    # full_response = ""
    # for token in response.response_gen:
    #     full_response += token
    # print(full_response)

    return index


# Main function to run the Streamlit app
def main():

    # Increase chunk sizes for longer context embeddings
    initialize_settings(
        chunk_size=3000,
    )

    col1, col2 = st.columns([1, 2])

    with col1:
        st.title("Multimodal RAG")

        input_method = st.radio(
            "Choose input method:",
            ("Use Existing Index", "Upload Files", "Enter Directory Path"),
        )

        if input_method == "Use Existing Index":
            with st.spinner("Loading existing index..."):
                index = create_or_load_index()
                if index is not None:
                    st.session_state["index"] = index
                    st.session_state["history"] = []
                    st.success("Existing index loaded successfully!")
                else:
                    st.error(
                        "No existing index found, and no documents provided to create one."
                    )
        elif input_method == "Upload Files":
            uploaded_files = st.file_uploader(
                "Drag and drop files here", accept_multiple_files=True
            )
            if uploaded_files and st.button("Process Files"):
                with st.spinner("Processing files..."):
                    documents = load_multimodal_data(uploaded_files)
                    st.session_state["index"] = create_or_load_index(documents)
                    st.session_state["history"] = []
                    st.success("Files processed and index created!")
        else:
            directory_path = st.text_input("Enter directory path:")
            if directory_path and st.button("Process Directory"):
                if os.path.isdir(directory_path):
                    with st.spinner("Processing directory..."):
                        documents = load_data_from_directory(directory_path)
                        st.session_state["index"] = create_or_load_index(documents)
                        st.session_state["history"] = []
                        st.success("Directory processed and index created!")
                else:
                    st.error("Invalid directory path. Please enter a valid path.")

    with col2:
        if "index" in st.session_state:
            st.title("Chat")
            if "history" not in st.session_state:
                st.session_state["history"] = []

            query_engine = st.session_state["index"].as_query_engine(
                similarity_top_k=5, streaming=True
            )

            user_input = st.chat_input("Enter your query:")

            # Display chat messages
            chat_container = st.container()
            with chat_container:
                for message in st.session_state["history"]:
                    with st.chat_message(message["role"]):
                        st.markdown(message["content"])

            if user_input:
                with st.chat_message("user"):
                    st.markdown(user_input)
                st.session_state["history"].append(
                    {"role": "user", "content": user_input}
                )

                with st.chat_message("assistant"):
                    message_placeholder = st.empty()
                    full_response = ""
                    response = query_engine.query(user_input)
                    for token in response.response_gen:
                        full_response += token
                        message_placeholder.markdown(full_response + "▌")
                    message_placeholder.markdown(full_response)
                st.session_state["history"].append(
                    {"role": "assistant", "content": full_response}
                )

            # Add a clear button
            if st.button("Clear Chat"):
                st.session_state["history"] = []
                st.rerun()


if __name__ == "__main__":
    main()
